"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ai-onboarding",{

/***/ "./src/components/ai-onboarding/DataCollectionForm.tsx":
/*!*************************************************************!*\
  !*** ./src/components/ai-onboarding/DataCollectionForm.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataCollectionForm: function() { return /* binding */ DataCollectionForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction DataCollectionForm(param) {\n    let { onSubmit, isLoading = false, selectedRole, initialData } = param;\n    var _initialData_location, _initialData_location1, _SYRIAN_LOCATIONS_formData_location_governorate, _formData_businessInfo, _formData_businessInfo1, _formData_businessInfo2;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: (initialData === null || initialData === void 0 ? void 0 : initialData.firstName) || \"\",\n        lastName: (initialData === null || initialData === void 0 ? void 0 : initialData.lastName) || \"\",\n        email: (initialData === null || initialData === void 0 ? void 0 : initialData.email) || \"\",\n        phoneNumber: (initialData === null || initialData === void 0 ? void 0 : initialData.phoneNumber) || \"\",\n        location: {\n            city: (initialData === null || initialData === void 0 ? void 0 : (_initialData_location = initialData.location) === null || _initialData_location === void 0 ? void 0 : _initialData_location.city) || \"\",\n            governorate: (initialData === null || initialData === void 0 ? void 0 : (_initialData_location1 = initialData.location) === null || _initialData_location1 === void 0 ? void 0 : _initialData_location1.governorate) || \"\"\n        },\n        servicePreferences: (initialData === null || initialData === void 0 ? void 0 : initialData.servicePreferences) || [],\n        projectTypes: (initialData === null || initialData === void 0 ? void 0 : initialData.projectTypes) || [],\n        businessInfo: (initialData === null || initialData === void 0 ? void 0 : initialData.businessInfo) || {\n            companyName: \"\",\n            industry: \"\",\n            size: \"\"\n        }\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Syrian governorates and major cities\n    const SYRIAN_LOCATIONS = {\n        \"دمشق\": [\n            \"دمشق\",\n            \"داريا\",\n            \"دوما\",\n            \"جرمانا\",\n            \"قدسيا\"\n        ],\n        \"ريف دمشق\": [\n            \"الزبداني\",\n            \"قطنا\",\n            \"التل\",\n            \"يبرود\",\n            \"النبك\"\n        ],\n        \"حلب\": [\n            \"حلب\",\n            \"منبج\",\n            \"عفرين\",\n            \"اعزاز\",\n            \"الباب\"\n        ],\n        \"حمص\": [\n            \"حمص\",\n            \"تدمر\",\n            \"القريتين\",\n            \"الرستن\",\n            \"تلبيسة\"\n        ],\n        \"حماة\": [\n            \"حماة\",\n            \"سلمية\",\n            \"مصياف\",\n            \"محردة\",\n            \"السقيلبية\"\n        ],\n        \"اللاذقية\": [\n            \"اللاذقية\",\n            \"جبلة\",\n            \"القرداحة\",\n            \"الحفة\"\n        ],\n        \"طرطوس\": [\n            \"طرطوس\",\n            \"بانياس\",\n            \"صافيتا\",\n            \"دريكيش\"\n        ],\n        \"إدلب\": [\n            \"إدلب\",\n            \"جسر الشغور\",\n            \"أريحا\",\n            \"معرة النعمان\"\n        ],\n        \"الحسكة\": [\n            \"الحسكة\",\n            \"القامشلي\",\n            \"رأس العين\",\n            \"المالكية\"\n        ],\n        \"دير الزور\": [\n            \"دير الزور\",\n            \"الميادين\",\n            \"البوكمال\",\n            \"الرقة\"\n        ],\n        \"الرقة\": [\n            \"الرقة\",\n            \"تل أبيض\",\n            \"الثورة\"\n        ],\n        \"درعا\": [\n            \"درعا\",\n            \"إزرع\",\n            \"الصنمين\",\n            \"نوى\"\n        ],\n        \"السويداء\": [\n            \"السويداء\",\n            \"شهبا\",\n            \"صلخد\",\n            \"القريا\"\n        ],\n        \"القنيطرة\": [\n            \"القنيطرة\",\n            \"فيق\",\n            \"خان أرنبة\"\n        ]\n    };\n    const SERVICE_CATEGORIES = {\n        EXPERT: [\n            \"تطوير المواقع\",\n            \"التصميم الجرافيكي\",\n            \"التسويق الرقمي\",\n            \"الترجمة\",\n            \"المحاسبة\",\n            \"الاستشارات القانونية\",\n            \"التصوير\",\n            \"الكتابة والتحرير\",\n            \"البرمجة\",\n            \"التدريس\",\n            \"الخدمات الهندسية\",\n            \"الخدمات الطبية\",\n            \"الكهرباء والصيانة\",\n            \"السباكة\",\n            \"النجارة\",\n            \"البناء والتشييد\"\n        ],\n        CLIENT: [\n            \"مشاريع تقنية\",\n            \"تصميم وإبداع\",\n            \"تسويق ومبيعات\",\n            \"خدمات تجارية\",\n            \"استشارات\",\n            \"خدمات منزلية\",\n            \"خدمات طبية\",\n            \"تعليم وتدريب\",\n            \"خدمات قانونية\",\n            \"خدمات مالية\"\n        ]\n    };\n    const BUSINESS_INDUSTRIES = [\n        \"التكنولوجيا\",\n        \"التجارة الإلكترونية\",\n        \"التعليم\",\n        \"الصحة\",\n        \"العقارات\",\n        \"السياحة\",\n        \"الصناعة\",\n        \"الزراعة\",\n        \"الخدمات المالية\",\n        \"الإعلام والاتصالات\",\n        \"النقل واللوجستيات\",\n        \"الطاقة\",\n        \"أخرى\"\n    ];\n    const BUSINESS_SIZES = [\n        \"شركة ناشئة (1-10 موظفين)\",\n        \"شركة صغيرة (11-50 موظف)\",\n        \"شركة متوسطة (51-200 موظف)\",\n        \"شركة كبيرة (200+ موظف)\"\n    ];\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required fields validation\n        if (!formData.firstName.trim()) {\n            newErrors.firstName = \"الاسم الأول مطلوب\";\n        }\n        if (!formData.lastName.trim()) {\n            newErrors.lastName = \"الاسم الأخير مطلوب\";\n        }\n        if (!formData.email.trim()) {\n            newErrors.email = \"البريد الإلكتروني مطلوب\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"البريد الإلكتروني غير صحيح\";\n        }\n        if (!formData.phoneNumber.trim()) {\n            newErrors.phoneNumber = \"رقم الهاتف مطلوب\";\n        } else if (!/^(\\+963|0)?[0-9]{8,9}$/.test(formData.phoneNumber.replace(/\\s/g, \"\"))) {\n            newErrors.phoneNumber = \"رقم الهاتف غير صحيح (يجب أن يكون رقم سوري صحيح)\";\n        }\n        if (!formData.location.governorate) {\n            newErrors.governorate = \"المحافظة مطلوبة\";\n        }\n        if (!formData.location.city) {\n            newErrors.city = \"المدينة مطلوبة\";\n        }\n        // Role-specific validation\n        if (selectedRole === \"EXPERT\" && (!formData.servicePreferences || formData.servicePreferences.length === 0)) {\n            newErrors.servicePreferences = \"يجب اختيار مجال خبرة واحد على الأقل\";\n        }\n        if (selectedRole === \"CLIENT\" && (!formData.projectTypes || formData.projectTypes.length === 0)) {\n            newErrors.projectTypes = \"يجب اختيار نوع مشروع واحد على الأقل\";\n        }\n        if (selectedRole === \"BUSINESS\") {\n            var _formData_businessInfo, _formData_businessInfo1, _formData_businessInfo2;\n            if (!((_formData_businessInfo = formData.businessInfo) === null || _formData_businessInfo === void 0 ? void 0 : _formData_businessInfo.companyName.trim())) {\n                newErrors.companyName = \"اسم الشركة مطلوب\";\n            }\n            if (!((_formData_businessInfo1 = formData.businessInfo) === null || _formData_businessInfo1 === void 0 ? void 0 : _formData_businessInfo1.industry)) {\n                newErrors.industry = \"مجال الشركة مطلوب\";\n            }\n            if (!((_formData_businessInfo2 = formData.businessInfo) === null || _formData_businessInfo2 === void 0 ? void 0 : _formData_businessInfo2.size)) {\n                newErrors.size = \"حجم الشركة مطلوب\";\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        try {\n            setIsLoading(true);\n            setErrors({});\n            // Save user data to API\n            const response = await fetch(\"/api/onboarding/save-user-data\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    role: selectedRole\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.message || \"Failed to save user data\");\n            }\n            if (data.success) {\n                // Call the parent onSubmit with the collected data\n                onSubmit(formData);\n            } else {\n                throw new Error(data.message || \"Failed to save user data\");\n            }\n        } catch (error) {\n            console.error(\"Failed to save user data:\", error);\n            setErrors({\n                submit: error.message || \"حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const handleLocationChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                location: {\n                    ...prev.location,\n                    [field]: value,\n                    // Reset city when governorate changes\n                    ...field === \"governorate\" && {\n                        city: \"\"\n                    }\n                }\n            }));\n        // Clear errors\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const handleBusinessInfoChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                businessInfo: {\n                    ...prev.businessInfo,\n                    [field]: value\n                }\n            }));\n        // Clear error\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const handleArrayFieldChange = (field, value)=>{\n        setFormData((prev)=>{\n            const currentArray = prev[field] || [];\n            const newArray = currentArray.includes(value) ? currentArray.filter((item)=>item !== value) : [\n                ...currentArray,\n                value\n            ];\n            return {\n                ...prev,\n                [field]: newArray\n            };\n        });\n        // Clear error\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const formatPhoneNumber = (phone)=>{\n        const cleaned = phone.replace(/\\D/g, \"\");\n        if (cleaned.startsWith(\"963\")) {\n            const number = cleaned.substring(3);\n            return \"+963 \".concat(number.substring(0, 2), \" \").concat(number.substring(2, 5), \" \").concat(number.substring(5));\n        } else if (cleaned.startsWith(\"0\")) {\n            const number = cleaned.substring(1);\n            return \"0\".concat(number.substring(0, 2), \" \").concat(number.substring(2, 5), \" \").concat(number.substring(5));\n        }\n        return phone;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        className: \"max-w-4xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                        children: \"إكمال المعلومات الشخصية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: [\n                            selectedRole === \"EXPERT\" && \"أكمل معلوماتك لإنشاء ملف خبير احترافي\",\n                            selectedRole === \"CLIENT\" && \"أكمل معلوماتك للعثور على أفضل الخبراء\",\n                            selectedRole === \"BUSINESS\" && \"أكمل معلومات شركتك لإدارة المشاريع بكفاءة\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6\",\n                                children: \"المعلومات الشخصية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"الاسم الأول *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.firstName,\n                                                onChange: (e)=>handleInputChange(\"firstName\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.firstName ? \"border-red-500\" : \"border-gray-300\"),\n                                                placeholder: \"أدخل الاسم الأول\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.firstName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"الاسم الأخير *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.lastName,\n                                                onChange: (e)=>handleInputChange(\"lastName\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.lastName ? \"border-red-500\" : \"border-gray-300\"),\n                                                placeholder: \"أدخل الاسم الأخير\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.lastName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"البريد الإلكتروني *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: formData.email,\n                                                onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.email ? \"border-red-500\" : \"border-gray-300\"),\n                                                placeholder: \"<EMAIL>\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"رقم الهاتف *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"tel\",\n                                                value: formData.phoneNumber,\n                                                onChange: (e)=>{\n                                                    const formatted = formatPhoneNumber(e.target.value);\n                                                    handleInputChange(\"phoneNumber\", formatted);\n                                                },\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.phoneNumber ? \"border-red-500\" : \"border-gray-300\"),\n                                                placeholder: \"+963 XX XXX XXXX أو 0XX XXX XXXX\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phoneNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.phoneNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                                children: \"أدخل رقم هاتف سوري صحيح\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6\",\n                                children: \"معلومات الموقع\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"المحافظة *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.location.governorate,\n                                                onChange: (e)=>handleLocationChange(\"governorate\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.governorate ? \"border-red-500\" : \"border-gray-300\"),\n                                                disabled: isLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"اختر المحافظة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    Object.keys(SYRIAN_LOCATIONS).map((governorate)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: governorate,\n                                                            children: governorate\n                                                        }, governorate, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.governorate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.governorate\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"المدينة *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.location.city,\n                                                onChange: (e)=>handleLocationChange(\"city\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.city ? \"border-red-500\" : \"border-gray-300\"),\n                                                disabled: isLoading || !formData.location.governorate,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"اختر المدينة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    formData.location.governorate && ((_SYRIAN_LOCATIONS_formData_location_governorate = SYRIAN_LOCATIONS[formData.location.governorate]) === null || _SYRIAN_LOCATIONS_formData_location_governorate === void 0 ? void 0 : _SYRIAN_LOCATIONS_formData_location_governorate.map((city)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: city,\n                                                            children: city\n                                                        }, city, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 21\n                                                        }, this)))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.city\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this),\n                                            !formData.location.governorate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                                children: \"اختر المحافظة أولاً\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    selectedRole === \"EXPERT\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6\",\n                                children: \"مجالات الخبرة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4\",\n                                        children: \"اختر مجالات خبرتك (يمكن اختيار أكثر من مجال) *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: SERVICE_CATEGORIES.EXPERT.map((service)=>{\n                                            var _formData_servicePreferences, _formData_servicePreferences1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200 \".concat(((_formData_servicePreferences = formData.servicePreferences) === null || _formData_servicePreferences === void 0 ? void 0 : _formData_servicePreferences.includes(service)) ? \"border-primary-500 bg-primary-50 dark:bg-primary-900/20\" : \"border-gray-300 dark:border-gray-600 hover:border-primary-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: ((_formData_servicePreferences1 = formData.servicePreferences) === null || _formData_servicePreferences1 === void 0 ? void 0 : _formData_servicePreferences1.includes(service)) || false,\n                                                        onChange: ()=>handleArrayFieldChange(\"servicePreferences\", service),\n                                                        className: \"sr-only\",\n                                                        disabled: isLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: service\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, service, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.servicePreferences && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-red-600\",\n                                        children: errors.servicePreferences\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, this),\n                    selectedRole === \"CLIENT\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6\",\n                                children: \"أنواع المشاريع\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4\",\n                                        children: \"ما نوع المشاريع التي تحتاج إليها؟ (يمكن اختيار أكثر من نوع) *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                        children: SERVICE_CATEGORIES.CLIENT.map((projectType)=>{\n                                            var _formData_projectTypes, _formData_projectTypes1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200 \".concat(((_formData_projectTypes = formData.projectTypes) === null || _formData_projectTypes === void 0 ? void 0 : _formData_projectTypes.includes(projectType)) ? \"border-primary-500 bg-primary-50 dark:bg-primary-900/20\" : \"border-gray-300 dark:border-gray-600 hover:border-primary-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: ((_formData_projectTypes1 = formData.projectTypes) === null || _formData_projectTypes1 === void 0 ? void 0 : _formData_projectTypes1.includes(projectType)) || false,\n                                                        onChange: ()=>handleArrayFieldChange(\"projectTypes\", projectType),\n                                                        className: \"sr-only\",\n                                                        disabled: isLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: projectType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, projectType, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.projectTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-red-600\",\n                                        children: errors.projectTypes\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 11\n                    }, this),\n                    selectedRole === \"BUSINESS\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6\",\n                                children: \"معلومات الشركة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"اسم الشركة *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_businessInfo = formData.businessInfo) === null || _formData_businessInfo === void 0 ? void 0 : _formData_businessInfo.companyName) || \"\",\n                                                onChange: (e)=>handleBusinessInfoChange(\"companyName\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.companyName ? \"border-red-500\" : \"border-gray-300\"),\n                                                placeholder: \"أدخل اسم الشركة\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.companyName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"مجال الشركة *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: ((_formData_businessInfo1 = formData.businessInfo) === null || _formData_businessInfo1 === void 0 ? void 0 : _formData_businessInfo1.industry) || \"\",\n                                                onChange: (e)=>handleBusinessInfoChange(\"industry\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.industry ? \"border-red-500\" : \"border-gray-300\"),\n                                                disabled: isLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"اختر مجال الشركة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    BUSINESS_INDUSTRIES.map((industry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: industry,\n                                                            children: industry\n                                                        }, industry, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.industry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.industry\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"حجم الشركة *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: ((_formData_businessInfo2 = formData.businessInfo) === null || _formData_businessInfo2 === void 0 ? void 0 : _formData_businessInfo2.size) || \"\",\n                                                onChange: (e)=>handleBusinessInfoChange(\"size\", e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white \".concat(errors.size ? \"border-red-500\" : \"border-gray-300\"),\n                                                disabled: isLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"اختر حجم الشركة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    BUSINESS_SIZES.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: size,\n                                                            children: size\n                                                        }, size, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-red-600\",\n                                                children: errors.size\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, this),\n                    errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 dark:text-red-400\",\n                            children: errors.submit\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            className: \"px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 text-white font-semibold rounded-xl hover:from-primary-700 hover:to-primary-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"جاري الحفظ...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 15\n                            }, this) : \"متابعة إلى المحادثة الذكية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                            lineNumber: 670,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\ai-onboarding\\\\DataCollectionForm.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, this);\n}\n_s(DataCollectionForm, \"0b3mHXoC+ngm2nXSjVazKHFJ9PM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = DataCollectionForm;\nvar _c;\n$RefreshReg$(_c, \"DataCollectionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ai-onboarding/DataCollectionForm.tsx\n"));

/***/ })

});